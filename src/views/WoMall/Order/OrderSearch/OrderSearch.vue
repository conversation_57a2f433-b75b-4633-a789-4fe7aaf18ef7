<template>
  <div class="order-search">
    <SearchHeader
      v-model="searchKeyword"
      placeholder="搜索我的订单"
      @search="handleSearch"
      class="order-search__header"
    />
    <div class="order-search__content" ref="contentRef">
      <OrderSearchSkeleton v-if="showSkeleton" />
      <OrderSearchEmpty v-if="!showSkeleton && !loading && orderList.length === 0 && finished" />

      <van-list
        v-if="!showSkeleton"
        v-model:loading="loading"
        :finished="finished"
        :finished-text="orderList.length > 0 ? '没有更多了' : ''"
        @load="onLoad"
        :immediate-check="false"
      >
        <article
          v-for="order in orderList"
          :key="order.id"
          class="order-search__item"
        >
          <WoCard>
            <header class="order-search__item-header">
              <div class="order-search__number-wrapper">
                <span class="order-search__number-text">订单号：{{ order.id }}</span>
                <img
                  src="@/static/images/copy.png"
                  alt="复制"
                  class="order-search__copy-icon"
                  @click.stop="copyOrderNumber(order.id)"
                />
              </div>
              <OrderCountdown
                v-if="order.orderState === '0' && order.remainingTime > 0"
                :remaining-time="order.remainingTime"
              />
              <div
                v-else
                class="order-search__status"
                :class="getStatusClass(order.orderState)"
              >
                {{ orderState(order.orderState) }}
              </div>
            </header>

            <section class="order-search__goods">
              <OrderGoodsCard
                :key="order.id"
                :item="order"
                :image-size="75"
                :min-height="110"
                :showActions="true"
                :moreActions="getMoreActions(order)"
                :itemId="order.id"
              >
                <template #actions>
                  <WoButton
                    v-for="button in getVisibleButtons(order)"
                    :key="button.text"
                    :type="getButtonType(button.color)"
                    size="small"
                    @click="button.handler"
                  >
                    {{ button.text }}
                  </WoButton>
                </template>
              </OrderGoodsCard>
            </section>
          </WoCard>
        </article>
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated, computed } from 'vue'
import { debounce, throttle } from 'lodash-es'
import SearchHeader from "@components/Common/SearchHeader.vue"
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderSearchSkeleton from './components/OrderSearchSkeleton.vue'
import OrderSearchEmpty from './components/OrderSearchEmpty.vue'
import OrderCountdown from './components/OrderCountdown.vue'
import useClipboard from 'vue-clipboard3'
import { closeToast, showLoadingToast, showToast } from 'vant'
import orderState from '@/utils/orderState.js'
import { useAlert } from '@/hooks/index.js'
import { getOrderSearchList } from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import { useRoute, useRouter } from 'vue-router'

const { toClipboard } = useClipboard()
const $alert = useAlert()
const route = useRoute()
const router = useRouter()

const searchKeyword = ref('')
const scrollPositions = ref({ all: 0 })
const isSearching = ref(false)
const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const page = ref(1)
const pageSize = ref(10)
const showSkeleton = ref(false)
const countdownTimers = ref(new Map())

const PAYMENT_TIMEOUT = 30 * 60 * 1000

const updateScrollPosition = (position) => {
  scrollPositions.value.all = position
}

const handleScroll = throttle(() => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    updateScrollPosition(scrollTop)
  }
}, 100)

const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber)
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}

const onLoad = async () => {
  if (page.value === 1 && orderList.value.length === 0) {
    showSkeleton.value = true
  } else {
    loading.value = true
  }

  const params = {
    disriBiz: getBizCode('ORDER'),
    searchContent: searchKeyword.value,
    pageNum: page.value,
    pageSize: pageSize.value
  }

  const [err, json] = await getOrderSearchList(params)

  if (!err) {
    page.value++
    loading.value = false
    showSkeleton.value = false

    if (json && json?.list.length <= 0) {
      loading.value = false
      finished.value = true
      isSearching.value = false
      return
    }

    const newOrders = json.list || []
    orderList.value = [...orderList.value, ...newOrders]

    orderList.value.forEach(item => {
      if (!item.hasOwnProperty('showPopover')) {
        item.showPopover = false
      }
    })

    newOrders.forEach(order => {
      if (order.orderState === '0' && order.createTime) {
        startCountdown(order)
      }
    })

    const totalPage = json.totalPage
    if (page.value > totalPage) {
      loading.value = true
      finished.value = true
    }

    showSkeleton.value = false

    if (page.value === 2 && scrollPositions.value.all > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPositions.value.all
        }
      })
    }
  } else {
    loading.value = false
    finished.value = true
    showSkeleton.value = false
    showToast('搜索失败，请重试')
  }
}

const performSearch = () => {
  if (!searchKeyword.value) {
    showToast('请输入搜索关键词')
    return
  }

  const newQuery = {
    ...route.query,
    keyword: searchKeyword.value
  }

  router.replace({
    path: route.path,
    query: newQuery
  })

  isSearching.value = true
  showSkeleton.value = true
  orderList.value = []
  page.value = 1
  finished.value = false
  loading.value = false

  clearAllCountdowns()

  nextTick(() => {
    onLoad()
  })
}

const handleSearch = debounce(performSearch, 300)

const startCountdown = (order) => {
  if (order.orderState !== '0' || !order.createTime) return

  if (countdownTimers.value.has(order.id)) {
    clearInterval(countdownTimers.value.get(order.id))
  }

  const updateCountdown = () => {
    const now = Date.now()
    const createTime = new Date(order.createTime).getTime()
    const elapsed = now - createTime
    const remaining = PAYMENT_TIMEOUT - elapsed

    if (remaining <= 0) {
      Object.assign(order, {
        remainingTime: 0,
        orderState: '2'
      })

      clearInterval(countdownTimers.value.get(order.id))
      countdownTimers.value.delete(order.id)
    } else {
      order.remainingTime = remaining
    }
  }

  updateCountdown()
  const timer = setInterval(updateCountdown, 1000)
  countdownTimers.value.set(order.id, timer)
}

const clearAllCountdowns = () => {
  countdownTimers.value.forEach(timer => clearInterval(timer))
  countdownTimers.value.clear()
}

const getStatusClass = computed(() => (state) => {
  const statusMap = {
    '0': 'order-search__status--unpaid',
    '3': 'order-search__status--unpaid',
    '5': 'order-search__status--unpaid',
    '9': 'order-search__status--completed'
  }
  return statusMap[state] || 'order-search__status--unpaid'
})

const getButtonType = (color) => {
  return color === 'orange' ? 'primary' : 'default'
}

const toShowViewLogistics = (item) => {
  return item.orderState === '5' || item.orderState === '9'
}

const toShowBananaTree = (item) => {
  return item.supplierDistriBiz?.distriBizCode === 'banana'
}

const createButtonConfig = (item) => {
  const buttons = []

  const canDelete = ['2', '9', '10'].includes(item.orderState)
  const canViewLogistics = toShowViewLogistics(item)
  const canShowBanana = toShowBananaTree(item)
  const canShowCertificate = item.supplierDistriBiz?.distriBizCode === 'fupin' &&
    ['1', '3', '5', '9'].includes(item.orderState)

  if (canDelete) {
    buttons.push({ text: '删除订单', color: 'gray', handler: () => onDeleteOrder(item) })
  }
  if (canViewLogistics) {
    buttons.push({ text: '查看物流', color: 'gray', handler: () => onExpressClick(item) })
  }
  if (canShowBanana) {
    buttons.push({ text: '我的香蕉树', color: 'gray', handler: () => onBananaClick() })
  }
  if (canShowCertificate) {
    buttons.push({ text: '荣誉证书', color: 'gray', handler: () => onCertificateClick(item) })
  }
  if (item.orderState === '0') {
    buttons.push({ text: '去支付', color: 'orange', handler: () => onPayClick(item) })
  }
  if (item.orderState === '3') {
    buttons.push({ text: '催发货', color: 'orange', handler: () => onUrgeShipment(item) })
  }
  if (item.orderState === '5') {
    buttons.push({ text: '确认收货', color: 'orange', handler: () => onConfirmReceipt(item) })
  }
  if (canDelete) {
    buttons.push({ text: '再次购买', color: 'orange', handler: () => onBuyClick(item) })
  }

  return buttons
}

const getVisibleButtons = (item) => {
  const allButtons = createButtonConfig(item)
  return allButtons.slice(0, 3)
}

const getMoreActions = (item) => {
  const allButtons = createButtonConfig(item)
  return allButtons.length > 3 ? allButtons.slice(3) : []
}

const onDeleteOrder = async (order) => {
  try {
    $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        showLoadingToast()
        setTimeout(() => {
          closeToast()
          const index = orderList.value.findIndex(item => item.id === order.id)
          if (index !== -1) {
            orderList.value.splice(index, 1)
          }
          showToast('删除成功')
        }, 1000)
      }
    })
  } catch (error) {
    // 用户取消操作
  }
}

const onExpressClick = (item) => {
  showToast('查看物流功能')
}

const onBananaClick = () => {
  showToast('我的香蕉树功能')
}

const onCertificateClick = (item) => {
  showToast('荣誉证书功能')
}

const onPayClick = (item) => {
  showToast('去支付功能')
}

const onUrgeShipment = (item) => {
  showToast('催发货功能')
}

const onConfirmReceipt = async (item) => {
  try {
    $alert({
      title: '确认收货',
      message: '确认收到商品了吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      messageAlign: 'center'
    })

    showLoadingToast()
    setTimeout(() => {
      closeToast()
      showToast('确认收货成功')
    }, 1000)
  } catch (error) {
    // 用户取消操作
  }
}

const onBuyClick = (item) => {
  showToast('再次购买功能')
}


onMounted(() => {
  const keyword = route.query.keyword
  if (keyword) {
    searchKeyword.value = keyword
    showSkeleton.value = true
  }

  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  if (keyword) {
    onLoad()
  }
})

onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
  clearAllCountdowns()
})

onActivated(() => {
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

onDeactivated(() => {
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.order-search {
  height: 100vh;
  display: flex;
  flex-direction: column;

  &__header {
    flex-shrink: 0;
  }

  &__content {
    flex: 1;
    overflow: auto;
    background-color: @bg-color-gray;
    padding: @padding-page * 2;
    .no-scrollbar();
  }

  &__item {
    margin-bottom: @padding-page * 2;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-wrapper {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__number-text {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &--unpaid {
      color: @theme-color;
    }

    &--completed {
      color: @text-color-secondary;
    }
  }

  &__goods {
    margin-bottom: @padding-page * 2;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

</style>

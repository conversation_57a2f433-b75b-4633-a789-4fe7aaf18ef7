<template>
  <div class="order-countdown">
    <span class="order-countdown__label">剩余</span>
    <span class="order-countdown__time">{{ formattedTime }}</span>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'

const props = defineProps({
  remainingTime: {
    type: Number,
    required: true
  }
})

const { remainingTime } = toRefs(props)

const formattedTime = computed(() => {
  if (remainingTime.value <= 0) return '00:00:00'

  const totalSeconds = Math.floor(remainingTime.value / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})
</script>

<style scoped lang="less">
.order-countdown {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  color: @theme-color;

  &__label {
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    margin-right: 4px;
    color: @theme-color;
  }

  &__time {
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @theme-color;
  }
}
</style>

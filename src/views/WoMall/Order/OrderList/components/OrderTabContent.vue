<template>
  <section class="order-tab-content" ref="contentRef">
    <OrderSkeleton v-if="showSkeleton" />

    <OrderEmptyState v-if="!showSkeleton && !loading && orderList.length === 0 && finished" />

    <van-list
      v-else
      v-model:loading="loading"
      :finished="finished"
      loading-text="加载中..."
      finished-text="没有更多了"
      @load="onLoad"
      :immediate-check="false"
    >
      <OrderItem
        v-for="order in orderList"
        :key="order.id"
        :order="order"
        :visible-buttons="getVisibleButtons(order)"
        :more-actions="getMoreActions(order)"
        @detail-click="onDetailClick"
        @copy-order="copyOrderNumber"
      />
    </van-list>

    <Certificate
      :show="certificate.show"
      :title="certificate.title"
      :date="certificate.date"
      :amt="certificate.amt"
      @close="onCertificateClose"
    />
  </section>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick, toRefs } from 'vue'
import { debounce } from 'lodash-es'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import useClipboard from 'vue-clipboard3'
import dayjs from 'dayjs'
import { formSubmit } from 'commonkit'

import OrderSkeleton from './OrderSkeleton.vue'
import OrderEmptyState from './OrderEmptyState.vue'
import OrderItem from './OrderItem.vue'
import Certificate from '../../components/Certificate/Certificate.vue'

import { cancelOrder, getOrderList, manualConfirmRecv, verifySupplierOrderRepurchased, modOrderListShow, getOrderExpress, repayOrder } from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/hooks/index.js'
import { buyProductCart, buyProductCartSession } from '@utils/storage.js'
import { fenToYuan } from '@utils/amount.js'

const { toClipboard } = useClipboard()
const $alert = useAlert()
const router = useRouter()

const props = defineProps({
  tabType: {
    type: String,
    required: true
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

const { tabType, scrollPosition } = toRefs(props)

const emit = defineEmits(['update-scroll'])

const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)
const error = ref(false)
const isRefreshing = ref(false)
const isTabSwitching = ref(false)

const certificate = ref({
  show: false,
  title: '',
  date: new Date(),
  amt: ''
})

const countdownTimers = ref(new Map())
const PAYMENT_TIMEOUT = 30 * 60 * 1000

const showSkeleton = computed(() => {
  return isTabSwitching.value || (loading.value && orderList.value.length === 0 && !error.value)
})

const toShowViewLogistics = (item) => {
  return item.orderState === '5' || item.orderState === '9'
}

const toShowBananaTree = (item) => {
  return item.supplierDistriBiz?.distriBizCode === 'banana'
}

const createButtonConfig = (text, color, handler) => ({ text, color, handler })

// 检查数组是否包含某个值（兼容性更好的方式）
const arrayIncludes = (arr, value) => arr.indexOf(value) !== -1

const getButtonsConfig = (item) => {
  const { orderState, supplierDistriBiz } = item
  const buttons = []

  // 删除订单按钮
  if (arrayIncludes(['2', '9', '10'], orderState)) {
    buttons.push(createButtonConfig('删除订单', 'gray', () => onDeleteOrder(item)))
  }

  // 待支付状态按钮
  if (orderState === '0') {
    buttons.push(createButtonConfig('取消订单', 'gray', () => onCancelClick(item.id)))
    buttons.push(createButtonConfig('去支付', 'orange', () => onPayClick(item.id)))
  }

  // 查看物流按钮
  if (toShowViewLogistics(item)) {
    buttons.push(createButtonConfig('查看物流', 'gray', () => onExpressClick(item)))
  }

  // 香蕉树按钮
  if (toShowBananaTree(item)) {
    buttons.push(createButtonConfig('我的香蕉树', 'gray', () => onBananaClick()))
  }

  // 荣誉证书按钮
  if (supplierDistriBiz?.distriBizCode === 'fupin' && arrayIncludes(['1', '3', '5', '9'], orderState)) {
    buttons.push(createButtonConfig('荣誉证书', 'gray', () => onCertificateClick(item)))
  }

  // 催发货按钮
  if (orderState === '3') {
    buttons.push(createButtonConfig('催发货', 'orange', () => onUrgeShipment(item)))
  }

  // 确认收货按钮
  if (orderState === '5') {
    buttons.push(createButtonConfig('确认收货', 'orange', () => onConfirmReceipt(item)))
  }

  // 再次购买按钮
  if (arrayIncludes(['2', '9', '10'], orderState)) {
    buttons.push(createButtonConfig('再次购买', 'orange', () => onBuyClick(item)))
  }

  return buttons
}

const getVisibleButtons = (item) => {
  const allButtons = getButtonsConfig(item)
  return allButtons.slice(0, 3)
}

const getMoreActions = (item) => {
  const allButtons = getButtonsConfig(item)
  return allButtons.length > 3 ? allButtons.slice(3) : []
}

const onDetailClick = (order) => {
  router.push({
    path: '/user/order/detail',
    query: {
      orderId: order.id,
      isPay: order.orderState === '0' ? '1' : '2'
    }
  })
}

const handleScroll = debounce(() => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}, 16)

const copyOrderNumber = debounce(async (orderNumber) => {
  try {
    await toClipboard(orderNumber)
    showToast('复制成功')
  } catch (e) {
    console.error(e)
    showToast('复制失败')
  }
}, 300)

watch(() => tabType.value, () => {
  clearAllCountdowns()
  isTabSwitching.value = true
  orderList.value = []
  currentPage.value = 1
  finished.value = false
  error.value = false
  loading.value = false

  nextTick(() => {
    onLoad()
  })
})

const processSupplierOrders = (order) => {
  if (!order.supplierOrderList?.length) {
    return order
  }

  let mergedSkuNumInfoList = []
  let totalPrice = 0

  order.supplierOrderList.forEach(supplierOrder => {
    if (supplierOrder.skuNumInfoList?.length) {
      mergedSkuNumInfoList = [...mergedSkuNumInfoList, ...supplierOrder.skuNumInfoList]
    }
    if (supplierOrder.price) {
      totalPrice += parseFloat(supplierOrder.price) || 0
    }
  })

  return {
    ...order,
    skuNumInfoList: mergedSkuNumInfoList,
    price: totalPrice > 0 ? totalPrice.toString() : order.price,
    totalPrice: totalPrice > 0 ? totalPrice.toString() : order.totalPrice
  }
}

const onLoad = async () => {
  const params = {
    disriBiz: getBizCode('ORDER'),
    orderState: tabType.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }

  showLoadingToast()
  const [err, json] = await getOrderList(params)
  closeToast()

  if (!err) {
    currentPage.value++
    loading.value = false



    if (json && json?.list.length <= 0) {
      finished.value = true
      isTabSwitching.value = false
      return
    }

    const newOrders = json.list || []
    const expandedOrders = newOrders.map(processSupplierOrders)
    if (isRefreshing.value) {
      orderList.value = expandedOrders
      isRefreshing.value = false
    } else if (currentPage.value === 2) {
      orderList.value = expandedOrders
    } else {
      orderList.value = [...orderList.value, ...expandedOrders]
    }

    orderList.value.forEach(item => {
      if (!Object.prototype.hasOwnProperty.call(item, 'showPopover')) {
        item.showPopover = false
      }
    })

    nextTick(() => {
      expandedOrders.forEach((order) => {
        if (order.orderState === '0') {
          const createTime = order.createTime || order.orderDate
          if (createTime) {
            const now = Date.now()
            const orderTime = new Date(createTime).getTime()
            const elapsed = now - orderTime
            const remaining = PAYMENT_TIMEOUT - elapsed

            const orderIndex = orderList.value.findIndex(o => o.id === order.id)
            if (orderIndex !== -1) {
              orderList.value[orderIndex].remainingTime = Math.max(0, remaining)

              if (orderList.value[orderIndex].remainingTime > 0) {
                startCountdown(orderList.value[orderIndex])
              } else {
                orderList.value[orderIndex].orderState = '2'
                orderList.value[orderIndex].remainingTime = 0
              }
            }
          }
        }
      })
    })

    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    isTabSwitching.value = false

    if (currentPage.value === 2 && scrollPosition.value > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPosition.value
        }
      })
    }
  } else {
    error.value = true
    loading.value = false
    finished.value = true
    isTabSwitching.value = false
  }
}

const startCountdown = (order) => {
  if (order.orderState !== '0') return

  const createTime = order.createTime || order.orderDate
  if (!createTime) return

  if (countdownTimers.value.has(order.id)) {
    clearInterval(countdownTimers.value.get(order.id))
  }

  const updateCountdown = () => {
    const now = Date.now()
    const orderTime = new Date(createTime).getTime()
    const elapsed = now - orderTime
    const remaining = PAYMENT_TIMEOUT - elapsed

    if (remaining <= 0) {
      const orderIndex = orderList.value.findIndex(o => o.id === order.id)
      if (orderIndex !== -1) {
        orderList.value[orderIndex].remainingTime = 0
        orderList.value[orderIndex].orderState = '2'
      }

      clearInterval(countdownTimers.value.get(order.id))
      countdownTimers.value.delete(order.id)
    } else {
      const orderIndex = orderList.value.findIndex(o => o.id === order.id)
      if (orderIndex !== -1) {
        orderList.value[orderIndex].remainingTime = remaining
      }
    }
  }

  updateCountdown()
  const timer = setInterval(updateCountdown, 1000)
  countdownTimers.value.set(order.id, timer)
}

const clearCountdown = (orderId) => {
  if (countdownTimers.value.has(orderId)) {
    clearInterval(countdownTimers.value.get(orderId))
    countdownTimers.value.delete(orderId)
  }
}

const clearAllCountdowns = () => {
  countdownTimers.value.forEach(timer => clearInterval(timer))
  countdownTimers.value.clear()
}

const resetData = () => {
  clearAllCountdowns()
  orderList.value = []
  currentPage.value = 1
  finished.value = false
  error.value = false
  loading.value = false
  isRefreshing.value = false
}

const onCancelClick = debounce(async (bizOrderId) => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    const [err] = await cancelOrder(bizOrderId)
    closeToast()
    if (!err) {
      const canceledOrder = orderList.value.find(order => order.bizOrderId === bizOrderId)
      if (canceledOrder) {
        clearCountdown(canceledOrder.id)
      }
      resetData()
      await onLoad()
    } else {
      showToast(err.msg)
    }
  }

  $alert({
    title: '',
    message: '取消后将无法恢复，您确定要取消订单吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    onConfirmCallback: async () => {
      await cancelOrderFn()
    }
  })
}, 300)

const onDeleteOrder = debounce(async (item) => {
  const deleteOrderFn = async () => {
    showLoadingToast()

    try {
      if (item.supplierOrderList?.length > 0) {
        const deletePromises = item.supplierOrderList.map(supplierOrder => {
          return modOrderListShow({
            supplierOrderId: supplierOrder.id,
            isDelete: 1
          })
        })

        const results = await Promise.all(deletePromises)
        const hasError = results.some(([err]) => err)

        if (hasError) {
          const errorResult = results.find(([err]) => err)
          throw new Error(errorResult[0].msg)
        }
      } else {
        const [err] = await modOrderListShow({
          supplierOrderId: item.id,
          isDelete: 1
        })
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()
      clearCountdown(item.id)

      const index = orderList.value.findIndex(order => order.id === item.id)
      if (index !== -1) {
        orderList.value[index].isDeleting = true
        setTimeout(() => {
          orderList.value.splice(index, 1)
        }, 500)
      }
    } catch (error) {
      closeToast()
      showToast(error.message || '删除失败')
    }
  }

  $alert({
    title: '',
    message: '确认删除该订单？',
    confirmButtonText: '确定',
    showCancelButton: true,
    cancelButtonText: '取消',
    onConfirmCallback: deleteOrderFn
  })
}, 300)

const onExpressClick = debounce(async (item) => {
  const { id, orderDate } = item
  const now = dayjs()
  const orderDateDayjs = dayjs(orderDate)
  const endTimeSub180 = now.subtract(12, 'month')
  const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

  if (isWithinScope) {
    showToast('物流信息已失效 ！')
    return
  }

  try {
    const [err, orderExpress] = await getOrderExpress(id)

    if (err) {
      showToast('查询物流信息失败')
      return
    }

    const { orderPackageList } = orderExpress
    if (orderPackageList?.length > 0) {
      router.push({
        name: 'user-order-entry-express',
        params: { orderExpress },
        query: { orderId: id }
      })
      return
    }

    showToast('物流信息已失效 ！')
  } catch (error) {
    console.error('查询物流信息失败:', error)
    showToast('查询物流信息失败')
  }
}, 300)

const onBananaClick = () => {
  router.push('/fpHome/banana-tree')
}

const onCertificateClick = (item) => {
  certificate.value.date = new Date(item.orderDate.replace(/-/g, '/'))
  certificate.value.title = '尊敬的沃钱包用户：'
  certificate.value.amt = fenToYuan(item.paymentDetail.actualPayAmount)
  certificate.value.show = true
}

const onCertificateClose = () => {
  certificate.value.show = false
}

const onPayClick = debounce(async (bizOrderId) => {
  showLoadingToast()
  try {
    const [res, json] = await repayOrder(bizOrderId)
    closeToast()

    if (res.code === '0000') {
      formSubmit(json.wapURL, { param: json.encryptContent })
    } else if (res.code === '2091070302' && res.data?.length > 0) {
      if (json.some(info => info.state === '2')) {
        showToast('您的订单中有商品已下架')
      } else if (json.some(info => info.state === '3')) {
        showToast('您的订单中有无货商品')
      } else if (json.some(info => info.state === '4')) {
        showToast('您的订单中有商品库存不足')
      }
    } else {
      showToast(res.msg)
    }
  } catch (error) {
    closeToast()
    console.error('支付失败:', error)
    showToast('支付失败，请重试')
  }
}, 300)

const onUrgeShipment = debounce((item) => {
  const { orderDate } = item
  const targetDate = dayjs(orderDate)
  const now = dayjs()
  const diff = now.diff(targetDate, 'millisecond')
  const diffInHours = diff / (1000 * 60 * 60)
  const isWithin48Hours = Math.abs(diffInHours) <= 48

  if (isWithin48Hours) {
    const dateAdd48 = targetDate.add(48, 'hour')
    const formattedDate = dateAdd48.format('M月DD日')
    $alert({
      messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:#FF780A;">${formattedDate}</span>前发货，请您耐心等待。</div>`,
      confirmButtonText: '确定',
      allowHtml: true,
      messageAlign: 'center'
    })
  } else {
    $alert({
      message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
      confirmButtonText: '确定',
      messageAlign: 'center'
    })
  }
}, 300)

const onConfirmReceipt = debounce(async (item) => {
  const confirmReceiptFn = async () => {
    showLoadingToast()

    try {
      if (item.supplierOrderList?.length > 0) {
        const confirmPromises = item.supplierOrderList.map(supplierOrder => {
          return manualConfirmRecv({ supplierOrderId: supplierOrder.id })
        })

        const results = await Promise.all(confirmPromises)
        const hasError = results.some(([err]) => err)

        if (hasError) {
          const errorResult = results.find(([err]) => err)
          throw new Error(errorResult[0].msg)
        }
      } else {
        const [err] = await manualConfirmRecv({ supplierOrderId: item.id })
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()
      resetData()
      onLoad()
    } catch (error) {
      closeToast()
      showToast(error.message || '操作失败')
    }
  }

  $alert({
    title: '确认收货',
    message: '确认收到商品了吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    messageAlign: 'center',
    onConfirmCallback: confirmReceiptFn
  })
}, 300)

const onBuyClick = debounce(async (orderInfo) => {
  const bizCode = getBizCode('ORDER')
  showLoadingToast()

  try {
    let allValidGoodsList = []
    let totalSkuCount = 0

    if (orderInfo.supplierOrderList?.length > 0) {
      const verifyPromises = orderInfo.supplierOrderList.map(supplierOrder => {
        return verifySupplierOrderRepurchased({
          bizCode,
          supplierOrderId: supplierOrder.id
        })
      })

      const results = await Promise.all(verifyPromises)

      results.forEach(([err, res], index) => {
        if (!err && res?.validGoodsList) {
          allValidGoodsList = [...allValidGoodsList, ...res.validGoodsList]
        }
        const supplierOrder = orderInfo.supplierOrderList[index]
        if (supplierOrder.skuNumInfoList) {
          totalSkuCount += supplierOrder.skuNumInfoList.length
        }
      })
    } else {
      const [err, res] = await verifySupplierOrderRepurchased({
        bizCode,
        supplierOrderId: orderInfo.id
      })

      if (!err && res) {
        allValidGoodsList = res.validGoodsList || []
      }
      totalSkuCount = orderInfo.skuNumInfoList?.length || 0
    }

    closeToast()

    if (allValidGoodsList.length === 0) {
      showToast('订单中的商品都卖光了，在看看其他商品吧~')
      return
    }

    buyProductCart.set(allValidGoodsList)
    buyProductCartSession.set(allValidGoodsList)

    if (totalSkuCount === allValidGoodsList.length) {
      router.push('/orderconfirm')
    } else if (totalSkuCount > allValidGoodsList.length) {
      $alert({
        title: '',
        message: '部分商品无货或已下架无法购买!',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        onConfirmCallback: () => router.push('/orderconfirm')
      })
    }
  } catch (error) {
    closeToast()
    console.error('再次购买检查失败:', error)
    showToast('操作失败，请重试')
  }
}, 300)

const startAllCountdowns = () => {
  orderList.value.forEach((order, index) => {
    if (order.orderState === '0') {
      const createTime = order.createTime || order.orderDate
      if (createTime) {
        const now = Date.now()
        const orderTime = new Date(createTime).getTime()
        const elapsed = now - orderTime
        const remaining = PAYMENT_TIMEOUT - elapsed

        orderList.value[index].remainingTime = Math.max(0, remaining)

        if (orderList.value[index].remainingTime > 0) {
          startCountdown(orderList.value[index])
        } else {
          orderList.value[index].orderState = '2'
          orderList.value[index].remainingTime = 0
        }
      }
    }
  })
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    clearAllCountdowns()
  } else {
    nextTick(() => {
      startAllCountdowns()
    })
  }
}

const refreshData = async () => {
  try {
    isRefreshing.value = true
    clearAllCountdowns()
    orderList.value = []
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = true
    await onLoad()
  } catch (error) {
    console.error('刷新数据失败:', error)
    isRefreshing.value = false
    throw error
  }
}

onMounted(() => {
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)
  loading.value = true
  onLoad()

  nextTick(() => {
    startAllCountdowns()
  })
})

onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }

  document.removeEventListener('visibilitychange', handleVisibilityChange)
  clearAllCountdowns()
})

defineExpose({
  refreshData
})

</script>

<style scoped lang="less">
.order-tab-content {
  min-height: calc(100vh - 134px);
  padding: 10px;
}

</style>

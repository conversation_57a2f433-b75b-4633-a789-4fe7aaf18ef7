<template>
  <article class="order-item">
    <WoCard>
      <header class="order-item__header">
        <div class="order-item__number-container">
          <span class="order-item__number-text">订单号：{{ order.id }}</span>
          <img 
            src="@/static/images/copy.png" 
            alt="复制" 
            class="order-item__copy-icon" 
            @click.stop="handleCopyOrder(order.id)" 
          />
        </div>
        
        <OrderCountdown 
          v-if="order.orderState === '0' && order.remainingTime > 0"
          :remaining-time="order.remainingTime"
        />
        
        <div 
          v-else 
          class="order-item__status" 
          :class="getStatusClass(order.orderState)"
        >
          {{ orderState(order.orderState) }}
        </div>
      </header>

      <section class="order-item__goods">
        <OrderGoodsCard 
          :key="order.id" 
          :item="order" 
          :image-size="75" 
          :min-height="110" 
          :showActions="true"
          :moreActions="moreActions" 
          :itemId="order.id" 
          @click="handleDetailClick(order)"
        >
          <template #actions>
            <WoButton 
              v-for="button in visibleButtons" 
              :key="button.text"
              :type="getButtonType(button.color)" 
              size="small" 
              @click.stop="button.handler"
            >
              {{ button.text }}
            </WoButton>
          </template>
        </OrderGoodsCard>
      </section>
    </WoCard>
  </article>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderCountdown from './OrderCountdown.vue'
import orderState from '@utils/orderState.js'

const props = defineProps({
  order: {
    type: Object,
    required: true
  },
  visibleButtons: {
    type: Array,
    default: () => []
  },
  moreActions: {
    type: Array,
    default: () => []
  }
})

const { order, visibleButtons, moreActions } = toRefs(props)

const emit = defineEmits(['detail-click', 'copy-order'])

const getStatusClass = (state) => {
  switch (state) {
    case '0':
    case '3':
    case '5':
      return 'order-item__status--unpaid'
    case '9':
      return 'order-item__status--completed'
    default:
      return 'order-item__status--completed'
  }
}

const getButtonType = (color) => {
  switch (color) {
    case 'orange':
      return 'gradient'
    case 'gray':
    default:
      return 'default'
  }
}

const handleDetailClick = (orderData) => {
  emit('detail-click', orderData)
}

const handleCopyOrder = (orderId) => {
  emit('copy-order', orderId)
}
</script>

<style scoped lang="less">
.order-item {
  margin-bottom: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__number-text {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &--unpaid {
      color: @theme-color;
    }

    &--completed {
      color: @text-color-secondary;
    }
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
